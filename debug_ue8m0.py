#!/usr/bin/env python3
"""
Debug script to identify why the UE8M0 fix isn't working.
"""
import sys
import os
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent / "python"))

def check_initial_state():
    """Check the initial state of DEEPGEMM_SCALE_UE8M0"""
    try:
        from sglang.srt.layers.quantization.deep_gemm_wrapper.configurer import (
            DEEPGEMM_SCALE_UE8M0,
            DEEPGEMM_BLACKWELL,
            _is_blackwell_arch
        )
        
        print("Initial State Check:")
        print(f"  DEEPGEMM_BLACKWELL: {DEEPGEMM_BLACKWELL}")
        print(f"  DEEPGEMM_SCALE_UE8M0: {DEEPGEMM_SCALE_UE8M0}")
        print(f"  _is_blackwell_arch(): {_is_blackwell_arch()}")
        
        # Check environment variables
        env_enable_deepgemm = os.environ.get("SGL_ENABLE_JIT_DEEPGEMM", "not set")
        env_ue8m0 = os.environ.get("SGL_ENABLE_DEEPGEMM_UE8M0", "not set")
        print(f"  SGL_ENABLE_JIT_DEEPGEMM: {env_enable_deepgemm}")
        print(f"  SGL_ENABLE_DEEPGEMM_UE8M0: {env_ue8m0}")
        
        return DEEPGEMM_SCALE_UE8M0, DEEPGEMM_BLACKWELL
        
    except ImportError as e:
        print(f"Failed to import: {e}")
        return None, None

def test_update_function():
    """Test the update function with debug output"""
    try:
        from sglang.srt.layers.quantization.deep_gemm_wrapper.configurer import (
            update_deepgemm_scale_ue8m0,
            DEEPGEMM_SCALE_UE8M0
        )
        
        print(f"\nBefore update: DEEPGEMM_SCALE_UE8M0 = {DEEPGEMM_SCALE_UE8M0}")
        
        # Test disabling UE8M0
        print("Calling update_deepgemm_scale_ue8m0(disable_ue8m0=True)...")
        update_deepgemm_scale_ue8m0(disable_ue8m0=True)
        
        # Re-import to get the updated value
        from importlib import reload
        import sglang.srt.layers.quantization.deep_gemm_wrapper.configurer as configurer_module
        reload(configurer_module)
        
        print(f"After update: DEEPGEMM_SCALE_UE8M0 = {configurer_module.DEEPGEMM_SCALE_UE8M0}")
        
        return configurer_module.DEEPGEMM_SCALE_UE8M0
        
    except Exception as e:
        print(f"Error in update function test: {e}")
        return None

def test_server_args():
    """Test with server args like the real scenario"""
    try:
        from sglang.srt.server_args import ServerArgs, prepare_server_args
        
        # Test args that should disable UE8M0
        args = prepare_server_args([
            "--model", "deepseek-ai/DeepSeek-R1", 
            "--tp-size", "8",
            "--ep-size", "8",
            "--trust-remote-code",
            "--disable-deepgemm-ue8m0"
        ])
        
        print(f"\nServer Args Test:")
        print(f"  disable_deepgemm_ue8m0: {args.disable_deepgemm_ue8m0}")
        
        # Now test the compile_utils integration
        from sglang.srt.layers.quantization.deep_gemm_wrapper.compile_utils import update_deep_gemm_config
        from sglang.srt.layers.quantization.deep_gemm_wrapper.configurer import DEEPGEMM_SCALE_UE8M0
        
        print(f"  Before update_deep_gemm_config: DEEPGEMM_SCALE_UE8M0 = {DEEPGEMM_SCALE_UE8M0}")
        
        update_deep_gemm_config(gpu_id=0, server_args=args)
        
        print(f"  After update_deep_gemm_config: DEEPGEMM_SCALE_UE8M0 = {DEEPGEMM_SCALE_UE8M0}")
        
        return DEEPGEMM_SCALE_UE8M0
        
    except Exception as e:
        print(f"Error in server args test: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_deepep_access():
    """Test how deepep.py accesses the value"""
    try:
        from sglang.srt.layers.quantization import deep_gemm_wrapper
        
        print(f"\nDeepEP Access Test:")
        print(f"  deep_gemm_wrapper.DEEPGEMM_SCALE_UE8M0: {deep_gemm_wrapper.DEEPGEMM_SCALE_UE8M0}")
        
        return deep_gemm_wrapper.DEEPGEMM_SCALE_UE8M0
        
    except Exception as e:
        print(f"Error in DeepEP access test: {e}")
        return None

def main():
    print("=" * 60)
    print("DEBUG: DeepSeek EP B200 UE8M0 Fix")
    print("=" * 60)
    
    # Check initial state
    initial_ue8m0, initial_blackwell = check_initial_state()
    
    # Test update function
    updated_ue8m0 = test_update_function()
    
    # Test server args integration
    server_args_ue8m0 = test_server_args()
    
    # Test DeepEP access
    deepep_ue8m0 = test_deepep_access()
    
    print("\n" + "=" * 60)
    print("SUMMARY:")
    print("=" * 60)
    print(f"Initial DEEPGEMM_SCALE_UE8M0: {initial_ue8m0}")
    print(f"After update function: {updated_ue8m0}")  
    print(f"After server args: {server_args_ue8m0}")
    print(f"DeepEP access: {deepep_ue8m0}")
    
    # Diagnosis
    if initial_ue8m0 is False:
        print("\n✓ DEEPGEMM_SCALE_UE8M0 was already False (likely not on Blackwell or env var disabled)")
    elif server_args_ue8m0 is False:
        print("\n✅ Fix is working correctly - UE8M0 disabled via server args")
    else:
        print("\n❌ Fix is NOT working - UE8M0 still enabled")
        print("   This could be due to:")
        print("   1. update_deepgemm_scale_ue8m0() not being called")
        print("   2. Global variable not being updated correctly")
        print("   3. Import timing issues")

if __name__ == "__main__":
    main()
