#!/usr/bin/env python3
"""
Simple script to start an SGLang server for testing LongBench v2 evaluation.
"""

import subprocess
import sys
import time
import requests
import argparse

def check_server(host="localhost", port=8000, timeout=5):
    """Check if server is running"""
    try:
        response = requests.get(f"http://{host}:{port}/v1/models", timeout=timeout)
        return response.status_code == 200
    except:
        return False

def start_server(model, host="0.0.0.0", port=8000):
    """Start SGLang server"""
    cmd = [
        sys.executable, "-m", "sglang.launch_server",
        "--model", model,
        "--host", host,
        "--port", str(port),
        "--trust-remote-code"
    ]
    
    print(f"🚀 Starting SGLang server...")
    print(f"   Model: {model}")
    print(f"   Host: {host}")
    print(f"   Port: {port}")
    print(f"   Command: {' '.join(cmd)}")
    
    # Start the server
    process = subprocess.Popen(cmd)
    
    # Wait for server to start
    print(f"\n⏳ Waiting for server to start...")
    for i in range(60):  # Wait up to 60 seconds
        if check_server("localhost", port):
            print(f"✅ Server is ready!")
            return process
        time.sleep(1)
        if i % 10 == 0:
            print(f"   Still waiting... ({i}s)")
    
    print(f"❌ Server failed to start within 60 seconds")
    process.terminate()
    return None

def main():
    parser = argparse.ArgumentParser(description="Start SGLang server for testing")
    parser.add_argument("--model", type=str, default="microsoft/DialoGPT-medium", 
                       help="Model to serve")
    parser.add_argument("--port", type=int, default=8000, help="Port number")
    parser.add_argument("--host", type=str, default="0.0.0.0", help="Host address")
    
    args = parser.parse_args()
    
    # Check if server is already running
    if check_server("localhost", args.port):
        print(f"✅ Server already running on port {args.port}")
        return 0
    
    # Start server
    process = start_server(args.model, args.host, args.port)
    
    if process:
        print(f"\n🎉 Server started successfully!")
        print(f"   You can now run LongBench v2 evaluation with:")
        print(f"   python run_longbench_v2_eval.py --model {args.model} --port {args.port} --num-examples 5")
        print(f"\n   Press Ctrl+C to stop the server")
        
        try:
            process.wait()
        except KeyboardInterrupt:
            print(f"\n🛑 Stopping server...")
            process.terminate()
            process.wait()
            print(f"✅ Server stopped")
        
        return 0
    else:
        return 1

if __name__ == "__main__":
    sys.exit(main())
