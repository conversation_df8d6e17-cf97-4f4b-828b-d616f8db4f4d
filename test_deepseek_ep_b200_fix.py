#!/usr/bin/env python3
"""
Test script to verify the DeepSeek EP B200 accuracy fix.

This script tests that the --disable-deepgemm-ue8m0 flag properly disables
the Blackwell-specific UE8M0 optimizations that can cause accuracy issues
with DeepSeek models on B200 GPUs.
"""

import os
import sys
import tempfile
import subprocess
import argparse
from pathlib import Path

# Add the SGLang path
sys.path.insert(0, str(Path(__file__).parent / "python"))

def test_server_arg_parsing():
    """Test that the new server argument is properly parsed."""
    from sglang.srt.server_args import ServerArgs, prepare_server_args
    
    # Test default behavior (UE8M0 enabled)
    args = prepare_server_args([
        "--model", "dummy-model",
        "--tp-size", "1"
    ])
    assert not args.disable_deepgemm_ue8m0, "Default should have UE8M0 enabled"
    
    # Test with flag (UE8M0 disabled)
    args = prepare_server_args([
        "--model", "dummy-model", 
        "--tp-size", "1",
        "--disable-deepgemm-ue8m0"
    ])
    assert args.disable_deepgemm_ue8m0, "Flag should disable UE8M0"
    
    print("✓ Server argument parsing test passed")

def test_deepgemm_configuration():
    """Test that the DeepGEMM configuration is properly updated."""
    from sglang.srt.layers.quantization.deep_gemm_wrapper.configurer import (
        update_deepgemm_scale_ue8m0, 
        DEEPGEMM_SCALE_UE8M0,
        DEEPGEMM_BLACKWELL
    )
    
    # Save original state
    original_ue8m0 = DEEPGEMM_SCALE_UE8M0
    
    try:
        # Test disabling UE8M0
        update_deepgemm_scale_ue8m0(disable_ue8m0=True)
        from sglang.srt.layers.quantization.deep_gemm_wrapper.configurer import DEEPGEMM_SCALE_UE8M0
        assert not DEEPGEMM_SCALE_UE8M0, "UE8M0 should be disabled"
        
        # Test enabling UE8M0 (should follow DEEPGEMM_BLACKWELL)
        update_deepgemm_scale_ue8m0(disable_ue8m0=False)
        from sglang.srt.layers.quantization.deep_gemm_wrapper.configurer import DEEPGEMM_SCALE_UE8M0
        # On non-Blackwell systems, this should be False regardless
        # On Blackwell systems, this should follow the env var
        
        print("✓ DeepGEMM configuration test passed")
        
    finally:
        # Restore original state (though this is a global change)
        pass

def test_integration():
    """Test the integration by checking that server args affect DeepGEMM config."""
    from sglang.srt.server_args import ServerArgs
    from sglang.srt.layers.quantization.deep_gemm_wrapper.compile_utils import update_deep_gemm_config
    from sglang.srt.layers.quantization.deep_gemm_wrapper.configurer import DEEPGEMM_SCALE_UE8M0
    
    # Create a mock ServerArgs with disable_deepgemm_ue8m0=True
    class MockServerArgs:
        def __init__(self):
            self.disable_deepgemm_ue8m0 = True
            self.chunked_prefill_size = 8192
    
    # Test that update_deep_gemm_config calls our function
    mock_args = MockServerArgs()
    
    try:
        update_deep_gemm_config(gpu_id=0, server_args=mock_args)
        from sglang.srt.layers.quantization.deep_gemm_wrapper.configurer import DEEPGEMM_SCALE_UE8M0
        assert not DEEPGEMM_SCALE_UE8M0, "Integration should disable UE8M0"
        print("✓ Integration test passed")
    except Exception as e:
        print(f"⚠ Integration test skipped due to missing dependencies: {e}")

def test_gsm8k_command():
    """Test that the GSM8K benchmark command includes the new flag."""
    
    # Test command for B200 with accuracy fix
    b200_command = [
        "python3", "-m", "sglang.launch_server",
        "--model", "deepseek-ai/DeepSeek-V3-0324",
        "--tp-size", "8",
        "--ep-size", "8", 
        "--trust-remote-code",
        "--disable-deepgemm-ue8m0"  # This is the fix
    ]
    
    # Test command for H200 (baseline)
    h200_command = [
        "python3", "-m", "sglang.launch_server", 
        "--model", "deepseek-ai/DeepSeek-V3-0324",
        "--tp-size", "8",
        "--ep-size", "8",
        "--trust-remote-code"
    ]
    
    print("✓ Commands for testing:")
    print(f"  B200 (with fix): {' '.join(b200_command)}")
    print(f"  H200 (baseline): {' '.join(h200_command)}")

def main():
    parser = argparse.ArgumentParser(description="Test DeepSeek EP B200 fix")
    parser.add_argument("--integration", action="store_true", 
                       help="Run integration tests (requires full SGLang setup)")
    args = parser.parse_args()
    
    print("Testing DeepSeek EP B200 accuracy fix...")
    print("=" * 50)
    
    try:
        test_server_arg_parsing()
        test_deepgemm_configuration()
        
        if args.integration:
            test_integration()
        else:
            print("⚠ Skipping integration test (use --integration to enable)")
            
        test_gsm8k_command()
        
        print("=" * 50)
        print("✅ All tests passed!")
        print("\nTo fix the DeepSeek EP accuracy issue on B200 GPUs:")
        print("Add --disable-deepgemm-ue8m0 to your server launch command")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
