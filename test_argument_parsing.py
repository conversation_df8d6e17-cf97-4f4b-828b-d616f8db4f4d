#!/usr/bin/env python3
"""
Test that the new --disable-deepgemm-ue8m0 argument is properly parsed.
"""

import sys
import argparse
from pathlib import Path

# Add the SGLang path
sys.path.insert(0, str(Path(__file__).parent / "python"))

def test_argument_parsing():
    """Test the argument parsing directly."""
    try:
        # Import the argument parser setup
        from sglang.srt.server_args import add_server_args
        
        # Create a parser and add server args
        parser = argparse.ArgumentParser()
        add_server_args(parser)
        
        # Test default behavior
        args1 = parser.parse_args([
            "--model", "test-model",
            "--tp-size", "1"
        ])
        
        assert hasattr(args1, 'disable_deepgemm_ue8m0'), "disable_deepgemm_ue8m0 attribute missing"
        assert not args1.disable_deepgemm_ue8m0, "Default should be False"
        
        # Test with the flag
        args2 = parser.parse_args([
            "--model", "test-model", 
            "--tp-size", "1",
            "--disable-deepgemm-ue8m0"
        ])
        
        assert args2.disable_deepgemm_ue8m0, "Flag should set to True"
        
        print("✓ Argument parsing test passed")
        return True
        
    except ImportError as e:
        print(f"⚠ Argument parsing test skipped: {e}")
        return False
    except Exception as e:
        print(f"✗ Argument parsing test failed: {e}")
        return False

def test_help_text():
    """Test that help text is available."""
    try:
        from sglang.srt.server_args import add_server_args
        
        parser = argparse.ArgumentParser()
        add_server_args(parser)
        
        # Get help text
        help_text = parser.format_help()
        
        assert "--disable-deepgemm-ue8m0" in help_text, "Flag not in help text"
        assert "B200" in help_text, "B200 not mentioned in help"
        assert "DeepSeek" in help_text, "DeepSeek not mentioned in help"
        
        print("✓ Help text test passed")
        return True
        
    except ImportError as e:
        print(f"⚠ Help text test skipped: {e}")
        return False
    except Exception as e:
        print(f"✗ Help text test failed: {e}")
        return False

def main():
    print("Testing argument parsing for DeepSeek EP B200 fix...")
    print("=" * 50)
    
    success1 = test_argument_parsing()
    success2 = test_help_text()
    
    print("=" * 50)
    
    if success1 and success2:
        print("🎉 All argument parsing tests passed!")
    elif success1 or success2:
        print("⚠ Some tests passed, some were skipped")
    else:
        print("❌ Tests failed or were skipped due to dependencies")
    
    print("\nThe fix adds the --disable-deepgemm-ue8m0 flag to disable")
    print("Blackwell-specific optimizations that cause accuracy issues")
    print("with DeepSeek models on B200 GPUs.")

if __name__ == "__main__":
    main()
