#!/usr/bin/env python3
"""
Simple script to run LongBench v2 evaluation for SGLang.

Usage:
    python run_longbench_v2_eval.py --model <model_name> --port <port> [options]

Example:
    python run_longbench_v2_eval.py --model meta-llama/Meta-Llama-3-8B-Instruct --port 8000 --num-examples 10
"""

import sys
import os
import argparse

# Add the python directory to the path so we can import sglang modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'python'))

def main():
    parser = argparse.ArgumentParser(description="Run LongBench v2 evaluation")
    parser.add_argument("--model", type=str, required=True, help="Model name")
    parser.add_argument("--port", type=int, default=8000, help="Port number")
    parser.add_argument("--host", type=str, default="localhost", help="Host address")
    parser.add_argument("--base-url", type=str, help="Base URL instead of host:port")
    parser.add_argument("--num-examples", type=int, default=50, help="Number of examples to evaluate")
    parser.add_argument("--num-threads", type=int, default=1, help="Number of threads")
    parser.add_argument("--dataset-path", type=str, default="THUDM/LongBench-v2", help="Dataset path")
    parser.add_argument("--categories", type=str, help="Comma-separated categories")
    parser.add_argument("--max-context-length", type=int, help="Max context length")
    parser.add_argument("--min-context-length", type=int, help="Min context length")
    parser.add_argument("--temperature", type=float, default=0.0, help="Temperature")
    
    args = parser.parse_args()
    
    # Set up environment
    if "OPENAI_API_KEY" not in os.environ:
        os.environ["OPENAI_API_KEY"] = "EMPTY"
    
    # Import the evaluation modules
    try:
        from sglang.test.simple_eval_longbench_v2 import LongBenchV2Eval
        from sglang.test.simple_eval_common import make_report, set_ulimit
    except ImportError as e:
        print(f"Error importing SGLang modules: {e}")
        print("Make sure SGLang is properly installed or run from the SGLang directory")
        return 1
    
    # Set ulimit
    set_ulimit()
    
    # Prepare base URL
    base_url = (
        f"{args.base_url}/v1" if args.base_url else f"http://{args.host}:{args.port}/v1"
    )
    
    print(f"🚀 Starting LongBench v2 evaluation...")
    print(f"   Model: {args.model}")
    print(f"   Base URL: {base_url}")
    print(f"   Dataset: {args.dataset_path}")
    print(f"   Examples: {args.num_examples}")
    print(f"   Temperature: {args.temperature}")
    
    # Parse categories if provided
    categories = None
    if args.categories:
        categories = args.categories.split(",")
        print(f"   Categories: {categories}")
    
    # Create evaluation object
    try:
        eval_obj = LongBenchV2Eval(
            data_source=args.dataset_path,
            num_examples=args.num_examples,
            num_threads=args.num_threads,
            categories=categories,
            max_context_length=args.max_context_length,
            min_context_length=args.min_context_length,
        )
        
        print(f"\n📊 Running evaluation...")
        
        # Run the evaluation
        result = eval_obj(
            model=args.model,
            base_url=base_url,
            temperature=args.temperature,
        )
        
        # Generate report
        report = make_report(result)
        print(f"\n📈 Results:")
        print(report)
        
        # Save results to file
        output_file = f"/tmp/longbench_v2_{args.model.replace('/', '_')}.json"
        
        # Save detailed results
        import json
        with open(output_file, 'w') as f:
            json.dump({
                'model': args.model,
                'dataset': args.dataset_path,
                'num_examples': args.num_examples,
                'temperature': args.temperature,
                'categories': categories,
                'results': result.__dict__ if hasattr(result, '__dict__') else str(result),
                'report': report
            }, f, indent=2)
        
        print(f"\n💾 Results saved to: {output_file}")
        print(f"\n✅ Evaluation completed successfully!")
        
        return 0
        
    except Exception as e:
        print(f"❌ Error during evaluation: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
