#!/usr/bin/env python3
"""
Comprehensive debug script to identify all potential issues with the UE8M0 fix.
"""
import sys
import os
from pathlib import Path

# Add the SGLang path
sys.path.insert(0, str(Path(__file__).parent / "python"))

def check_all_ue8m0_references():
    """Find all references to DEEPGEMM_SCALE_UE8M0 and DEEPGEMM_BLACKWELL"""
    print("🔍 Checking all UE8M0 references...")
    
    # Files that reference DEEPGEMM_SCALE_UE8M0 or DEEPGEMM_BLACKWELL
    references = [
        "python/sglang/srt/layers/moe/token_dispatcher/deepep.py",
        "python/sglang/srt/models/longcat_flash.py", 
        "python/sglang/srt/models/deepseek_v2.py",
        "python/sglang/srt/models/longcat_flash_nextn.py"
    ]
    
    issues = []
    
    for file_path in references:
        full_path = Path(file_path)
        if not full_path.exists():
            continue
            
        content = full_path.read_text()
        
        # Check for problematic patterns
        if "DEEPGEMM_BLACKWELL" in content and "dispatch" in content:
            if "DEEPGEMM_SCALE_UE8M0" not in content:
                issues.append(f"❌ {file_path}: Still uses DEEPGEMM_BLACKWELL in dispatch logic")
        
        if "deep_gemm_wrapper.DEEPGEMM_SCALE_UE8M0 = False" in content:
            issues.append(f"❌ {file_path}: Has hardcoded DEEPGEMM_SCALE_UE8M0 = False")
    
    if issues:
        for issue in issues:
            print(issue)
        return False
    else:
        print("✅ All references look correct")
        return True

def simulate_server_arg_flow():
    """Simulate what happens when server args are processed"""
    print("\n🔧 Simulating server argument flow...")
    
    # Check initial configurer state
    try:
        from sglang.srt.layers.quantization.deep_gemm_wrapper.configurer import (
            DEEPGEMM_SCALE_UE8M0,
            DEEPGEMM_BLACKWELL,
            update_deepgemm_scale_ue8m0
        )
        
        print(f"  Initial DEEPGEMM_BLACKWELL: {DEEPGEMM_BLACKWELL}")
        print(f"  Initial DEEPGEMM_SCALE_UE8M0: {DEEPGEMM_SCALE_UE8M0}")
        
        # Test the update function
        print(f"  Calling update_deepgemm_scale_ue8m0(disable_ue8m0=True)...")
        update_deepgemm_scale_ue8m0(disable_ue8m0=True)
        
        print(f"  After update: DEEPGEMM_SCALE_UE8M0 = {DEEPGEMM_SCALE_UE8M0}")
        
        # Test how deepep accesses it
        from sglang.srt.layers.quantization import deep_gemm_wrapper
        print(f"  DeepEP access: deep_gemm_wrapper.DEEPGEMM_SCALE_UE8M0 = {deep_gemm_wrapper.DEEPGEMM_SCALE_UE8M0}")
        
        return deep_gemm_wrapper.DEEPGEMM_SCALE_UE8M0 == False
        
    except ImportError as e:
        print(f"  Import error (expected in test env): {e}")
        return None

def check_environment_interactions():
    """Check if environment variables could interfere"""
    print("\n🌍 Checking environment variable interactions...")
    
    env_vars = [
        "SGL_ENABLE_JIT_DEEPGEMM",
        "SGL_ENABLE_DEEPGEMM_UE8M0"
    ]
    
    for var in env_vars:
        value = os.environ.get(var, "not set")
        print(f"  {var}: {value}")
        
        if var == "SGL_ENABLE_DEEPGEMM_UE8M0" and value.lower() == "true":
            print(f"    ⚠️  {var}=true might override server arg disable")

def find_missing_fixes():
    """Look for other files that might need similar fixes"""
    print("\n🔍 Looking for potentially missing fixes...")
    
    # Check deepseek_v2.py for similar issues
    deepseek_v2_path = Path("python/sglang/srt/models/deepseek_v2.py")
    if deepseek_v2_path.exists():
        content = deepseek_v2_path.read_text()
        if "DEEPGEMM_BLACKWELL" in content:
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if "DEEPGEMM_BLACKWELL" in line and "dispatch" not in line:
                    print(f"  ⚠️  deepseek_v2.py:{i+1}: {line.strip()}")
    
    # Check other model files
    model_files = [
        "python/sglang/srt/models/longcat_flash_nextn.py"
    ]
    
    for file_path in model_files:
        full_path = Path(file_path)
        if full_path.exists():
            content = full_path.read_text()
            if "deep_gemm_wrapper.DEEPGEMM_SCALE_UE8M0 = False" in content:
                print(f"  ❌ {file_path}: Has hardcoded DEEPGEMM_SCALE_UE8M0 = False")

def create_test_scenario():
    """Create a test scenario to validate the fix works"""
    print("\n🧪 Test Scenario Analysis...")
    
    print("Expected behavior:")
    print("1. On non-Blackwell GPU: DEEPGEMM_SCALE_UE8M0 should be False (no UE8M0 optimization)")
    print("2. On Blackwell GPU without --disable flag: DEEPGEMM_SCALE_UE8M0 should be True")
    print("3. On Blackwell GPU with --disable flag: DEEPGEMM_SCALE_UE8M0 should be False")
    print("4. Environment SGL_ENABLE_DEEPGEMM_UE8M0=false should also make it False")
    
    print("\nTo test the fix:")
    print("zhyncs should run:")
    print("export SGL_ENABLE_DEEPGEMM_UE8M0=false")
    print("python3 -m sglang.launch_server --model deepseek-ai/DeepSeek-R1 --tp-size 8 --ep-size 8 --trust-remote-code")
    print("OR")
    print("python3 -m sglang.launch_server --model deepseek-ai/DeepSeek-R1 --tp-size 8 --ep-size 8 --trust-remote-code --disable-deepgemm-ue8m0")

def main():
    print("=" * 60)
    print("COMPREHENSIVE DEBUG: DeepSeek EP B200 UE8M0 Fix")
    print("=" * 60)
    
    # Run all checks
    refs_ok = check_all_ue8m0_references()
    server_flow_ok = simulate_server_arg_flow()
    check_environment_interactions()
    find_missing_fixes()
    create_test_scenario()
    
    print("\n" + "=" * 60)
    print("DIAGNOSIS:")
    print("=" * 60)
    
    if refs_ok and server_flow_ok is not False:
        print("✅ Fix appears to be implemented correctly")
        print("🔧 If still not working, the issue might be:")
        print("   1. Timing: Model init happens after server arg processing") 
        print("   2. Multiple processes: Each rank needs the same config")
        print("   3. Environment override: SGL_ENABLE_DEEPGEMM_UE8M0=true")
        print("   4. Cache: Compiled kernels might be cached with old settings")
    else:
        print("❌ Fix implementation has issues - see above for details")

if __name__ == "__main__":
    main()
