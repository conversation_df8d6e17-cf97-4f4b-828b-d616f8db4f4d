#!/usr/bin/env python3
"""
Mock SGLang server for testing LongBench v2 evaluation.
This creates a simple FastAPI server that mimics SGLang's API.
"""

from fastapi import FastAPI
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import uvicorn
import time
import random

app = FastAPI()

class Message(BaseModel):
    role: str
    content: str

class ChatCompletionRequest(BaseModel):
    model: str
    messages: List[Message]
    temperature: Optional[float] = 0.0
    max_tokens: Optional[int] = 2048
    stream: Optional[bool] = False

class ChatCompletionChoice(BaseModel):
    index: int
    message: Message
    finish_reason: str

class ChatCompletionResponse(BaseModel):
    id: str
    object: str
    created: int
    model: str
    choices: List[ChatCompletionChoice]

class ModelInfo(BaseModel):
    id: str
    object: str
    created: int
    owned_by: str

@app.get("/v1/models")
async def list_models():
    """List available models"""
    return {
        "object": "list",
        "data": [
            {
                "id": "mock-model",
                "object": "model",
                "created": int(time.time()),
                "owned_by": "mock-sglang"
            }
        ]
    }

@app.post("/v1/chat/completions")
async def chat_completions(request: ChatCompletionRequest):
    """Handle chat completion requests"""
    
    # Get the user message
    user_message = ""
    for msg in request.messages:
        if msg.role == "user":
            user_message = msg.content
            break
    
    # Simple mock responses for different types of questions
    response_text = generate_mock_response(user_message)
    
    # Simulate some processing time
    await asyncio.sleep(0.1)
    
    return ChatCompletionResponse(
        id=f"chatcmpl-{random.randint(1000000, 9999999)}",
        object="chat.completion",
        created=int(time.time()),
        model=request.model,
        choices=[
            ChatCompletionChoice(
                index=0,
                message=Message(role="assistant", content=response_text),
                finish_reason="stop"
            )
        ]
    )

def generate_mock_response(user_message: str) -> str:
    """Generate a mock response based on the user message"""
    
    # Convert to lowercase for easier matching
    message_lower = user_message.lower()
    
    # Look for multiple choice questions
    if "a." in message_lower and "b." in message_lower:
        # This looks like a multiple choice question
        choices = ["A", "B", "C", "D"]
        
        # Try to give somewhat reasonable answers based on keywords
        if "fox" in message_lower:
            return "B"
        elif "python" in message_lower and "guido" in message_lower:
            return "B"
        elif "mars" in message_lower and "orbital" in message_lower:
            return "B"
        elif "earth" in message_lower and "sun" in message_lower:
            return "A"
        else:
            # Random choice for other questions
            return random.choice(choices)
    
    # For non-multiple choice questions, provide a generic response
    if "what" in message_lower:
        return "Based on the context provided, the answer is related to the key information mentioned in the passage."
    elif "who" in message_lower:
        return "The person mentioned in the context is the relevant individual."
    elif "when" in message_lower:
        return "The time period mentioned in the context is the relevant timeframe."
    elif "where" in message_lower:
        return "The location mentioned in the context is the relevant place."
    elif "why" in message_lower:
        return "The reason is explained in the provided context."
    elif "how" in message_lower:
        return "The method is described in the given information."
    else:
        return "Based on the provided context, I can answer this question using the information given."

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy"}

@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "Mock SGLang Server", "version": "1.0.0"}

if __name__ == "__main__":
    import asyncio
    
    print("🚀 Starting Mock SGLang Server...")
    print("   Host: 0.0.0.0")
    print("   Port: 8000")
    print("   API: http://localhost:8000/v1")
    print("   Health: http://localhost:8000/health")
    print("   Models: http://localhost:8000/v1/models")
    
    uvicorn.run(app, host="0.0.0.0", port=8000)
