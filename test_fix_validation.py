#!/usr/bin/env python3
"""
Test script to validate the DeepSeek EP B200 accuracy fix.
This script can be run to validate the fix without full SGLang dependencies.
"""
import sys
from pathlib import Path

def validate_longcat_fix():
    """Validate that longcat_flash.py no longer has the hardcoded DEEPGEMM_SCALE_UE8M0 = False"""
    longcat_path = Path("python/sglang/srt/models/longcat_flash.py")
    
    if not longcat_path.exists():
        return False, "longcat_flash.py not found"
    
    content = longcat_path.read_text()
    
    # Check that the problematic line is gone
    if "deep_gemm_wrapper.DEEPGEMM_SCALE_UE8M0 = False" in content:
        return False, "Hardcoded DEEPGEMM_SCALE_UE8M0 = False still present in longcat_flash.py"
    
    # Check that there's a comment explaining the change  
    if "server args" not in content or "9943" not in content:
        return False, "Missing explanatory comment about server args control"
    
    return True, "longcat_flash.py fix validated"

def validate_configurer_improvement():
    """Validate that the configurer function has improved logic"""
    configurer_path = Path("python/sglang/srt/layers/quantization/deep_gemm_wrapper/configurer.py")
    
    if not configurer_path.exists():
        return False, "configurer.py not found"
    
    content = configurer_path.read_text()
    
    # Check for improved logic (should not have "and DEEPGEMM_SCALE_UE8M0" in the condition)
    if "if disable_ue8m0 and DEEPGEMM_SCALE_UE8M0:" in content:
        return False, "Old buggy logic still present in configurer.py"
    
    # Check for new logic
    if "if disable_ue8m0:" not in content:
        return False, "Improved logic not found in configurer.py"
    
    # Check for documentation about the fix
    if "#9943" not in content:
        return False, "Missing reference to GitHub issue #9943"
    
    return True, "configurer.py improvement validated"

def main():
    print("Validating DeepSeek EP B200 accuracy fix...")
    print("=" * 50)
    
    checks = [
        ("Longcat Flash Fix", validate_longcat_fix),
        ("Configurer Improvement", validate_configurer_improvement),
    ]
    
    all_passed = True
    
    for name, check_func in checks:
        try:
            success, message = check_func()
            status = "✓" if success else "✗"
            print(f"{status} {name}: {message}")
            if not success:
                all_passed = False
        except Exception as e:
            print(f"✗ {name}: Error during validation - {e}")
            all_passed = False
    
    print("=" * 50)
    
    if all_passed:
        print("🎉 All validations passed!")
        print("\nThe fix should now work correctly:")
        print("1. Server args properly control DEEPGEMM_SCALE_UE8M0")
        print("2. Model initialization doesn't override server settings")
        print("3. B200 GPUs should get correct accuracy with --disable-deepgemm-ue8m0")
        return 0
    else:
        print("❌ Some validations failed.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
