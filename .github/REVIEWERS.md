# Area Reviewer

Here are some reviewers for common areas. You can ping them to review your code if you touch related parts.

## Hardware platforms
- general @Alcanderian
- AMD GPU @HaiShaw
- Blackwell GPU @kushanam @trevor-m @zhyncs
- CPU @mingfeima

## Kernel
- general @zhyncs @ispobock @HandH1998 @BBuf @yizhang2077 @HaiShaw
- triton attention backend @ispobock
- aiter attention backend @HaiShaw @kkHuang-amd @valarLip
- flash attention backend @hebiao064
- flashinfer attention backend @Fridge003
- moe kernel @BBuf @fzyzcjy @ch-wan @Alcanderian

## Scheduler and memory pool
- general @merrymercy @Ying1123 @hnyls2002 @xiezhq-hermann
- constrained decoding @hnyls2002
- hierarchical cache @xiezhq-hermann @DarkSharpness
- lora @Fridge003 @Ying1123 @lifuhuang
- speculative decoding @merrymercy @Ying1123 @kssteven418 @Qiaolin-Yu
- sliding window attention @hanming-lu

## Parallelism
- expert parallelism @fzyzcjy @ch-wan
- data parallelism attention @ch-wan
- pipeline parallelism @Ying1123
- tensor parallelism @merrymercy

## PD disaggregation
- general @ByronHsu @ShangmingCai @hnyls2002
- Mooncake backend @ShangmingCai

## Build and release
- general @zhyncs @merrymercy

## API Server
- general @CatherineSue @slin1237 @ispobock
- function calling and reasoning parsing @CatherineSue
- OpenAI API @CatherineSue @slin1237

## SGL-Router
- general @slin1237 @ByronHsu

## Model
- multimodal models @mickqian @JustinTong0323
- other new models @zhaochenyang20

## Reinforcment learning
- general @zhaochenyang20 @hebiao064 @fzyzcjy @zhuzilin
