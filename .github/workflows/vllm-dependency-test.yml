name: VLLM Dependency Test

on:
  push:
    branches: [ main ]
    paths:
      - "python/**"
      - "scripts/ci/**"
      - "test/**"
  pull_request:
    branches: [ main ]
    paths:
      - "python/**"
      - "scripts/ci/**"
      - "test/**"

concurrency:
  group: vllm-dependency-test-${{ github.ref }}
  cancel-in-progress: true

jobs:
  vllm-dependency-test:
    if: (github.repository == 'sgl-project/sglang' || github.event_name == 'pull_request') &&
        github.event.pull_request.draft == false
    runs-on: 1-gpu-runner
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install dependencies
        run: |
          bash scripts/ci/ci_install_dependency.sh
          pip install "bitsandbytes>=0.44.0"

          pip install "sgl-kernel==0.3.7"

      - name: Run vLLM dependency tests
        timeout-minutes: 60
        run: |
          export SGLANG_SKIP_SGL_KERNEL_VERSION_CHECK=1

          cd test/srt
          python3 run_suite.py --suite vllm_dependency_test --timeout-per-file 3600
