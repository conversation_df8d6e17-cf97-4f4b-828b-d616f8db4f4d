name: PR Test (H20)

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      version:
        required: true
        type: choice
        default: 'release'
        options:
          - 'release'
          - 'nightly'

concurrency:
  group: pr-test-h20-${{ github.ref }}
  cancel-in-progress: true

jobs:
  check-changes:
    runs-on: ubuntu-latest
    outputs:
      src: ${{ steps.filter.outputs.src }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Detect file changes
        id: filter
        uses: dorny/paths-filter@v3
        with:
          filters: |
            src:
              - "python/sglang/srt/models/deepseek*"
              - "python/sglang/srt/layers/moe/**"
              - ".github/workflows/pr-test-h20.yml"

  per-commit-8-gpu-h20:
    needs: [check-changes]
    if: (github.repository == 'sgl-project/sglang' || github.event_name == 'pull_request') &&
        github.event.pull_request.draft == false &&
        needs.check-changes.outputs.src == 'true'
    runs-on: 8-gpu-h20
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install dependencies
        run: |
          bash scripts/ci/ci_install_dependency.sh

      - name: Run test
        timeout-minutes: 20

        run: |
          cd test/srt
          python3 run_suite.py --suite per-commit-8-gpu-h20

  pr-test-h20-finish:
    needs: [
      check-changes,
      per-commit-8-gpu-h20,
    ]
    if: needs.check-changes.outputs.src == 'true'
    runs-on: ubuntu-latest
    steps:
      - name: Check all dependent job statuses
        run: |
          results=(${{ join(needs.*.result, ' ') }})
          for result in "${results[@]}"; do
            if [ "$result" = "failure" ] || [ "$result" = "cancelled" ]; then
              echo "Job failed with result: $result"
              exit 1
            fi
          done
          echo "All jobs completed successfully"
          exit 0
