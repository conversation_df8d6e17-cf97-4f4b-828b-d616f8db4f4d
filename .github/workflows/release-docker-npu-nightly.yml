name: Release Docker Images Nightly (Ascend NPU)
on:
  pull_request:
    branches:
      - main
    paths:
      - ".github/workflows/release-docker-npu-nightly.yml"
  workflow_dispatch:
  schedule:
    - cron: "0 0 * * *"

concurrency:
  group: ${{ github.workflow }}-${{ github.sha }}
  cancel-in-progress: true

jobs:
  build:
    runs-on: ubuntu-22.04-arm
    strategy:
      matrix:
        cann_version: ["8.2.rc1"]
        device_type: ["910b", "a3"]
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Free up disk space
        uses: jlumbroso/free-disk-space@54081f138730dfa15788a46383842cd2f914a1be # v1.3.1
        with:
          tool-cache: true
          docker-images: false

      - name: Setup Docker buildx
        uses: docker/setup-buildx-action@v3

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: |
            lmsysorg/sglang
          # push with schedule event
          # push with workflow_dispatch event
          tags: |
            type=ref,event=pr
            type=ref,event=branch
            type=schedule,pattern=main
          flavor: |
            latest=false
            suffix=-cann${{ matrix.cann_version }}-${{ matrix.device_type }},onlatest=true
      # Login against a Docker registry except on PR
      # https://github.com/docker/login-action
      - name: Log into docker hub
        uses: docker/login-action@v3
        if: ${{ github.repository == 'sgl-project/sglang' && github.event_name != 'pull_request' }}
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      # Build and push Docker image with Buildx (don't push on PR)
      # https://github.com/docker/build-push-action
      - name: Build and push Docker image
        id: build-and-push
        uses: docker/build-push-action@v6
        with:
          context: docker
          file: docker/Dockerfile.npu
          # TODO: need add x86 platforms support when memfabric is ready
          platforms: linux/arm64
          labels: ${{ steps.meta.outputs.labels }}
          tags: ${{ steps.meta.outputs.tags }}
          push: ${{ github.repository == 'sgl-project/sglang' && github.event_name != 'pull_request' }}
          provenance: false
          build-args: |
            CANN_VERSION=${{ matrix.cann_version }}
            DEVICE_TYPE=${{ matrix.device_type }}
