#!/usr/bin/env python3
"""
Standalone LongBench v2 demonstration script.
This script shows how the evaluation works without needing a running server.
"""

import json
import os
import sys
from typing import List, Dict, Any
from pathlib import Path

# Add the python directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'python'))

def load_longbench_v2_sample():
    """Load a sample from the downloaded LongBench v2 dataset"""
    
    # Try to find the cached dataset
    cache_dirs = [
        os.path.expanduser("~/.cache/huggingface/datasets"),
        os.path.expanduser("~/.cache/huggingface"),
        "/tmp/huggingface",
    ]
    
    dataset_path = None
    for cache_dir in cache_dirs:
        potential_path = os.path.join(cache_dir, "datasets", "THUDM___long_bench-v2")
        if os.path.exists(potential_path):
            dataset_path = potential_path
            break
    
    if dataset_path:
        print(f"✅ Found cached dataset at: {dataset_path}")
        
        # Try to find the data file
        for root, dirs, files in os.walk(dataset_path):
            for file in files:
                if file.endswith('.json') and 'data' in file:
                    data_file = os.path.join(root, file)
                    print(f"   Loading data from: {data_file}")
                    
                    try:
                        with open(data_file, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            if isinstance(data, list) and len(data) > 0:
                                return data[:5]  # Return first 5 examples
                            elif isinstance(data, dict):
                                # Handle different data formats
                                if 'data' in data:
                                    return data['data'][:5]
                                elif 'examples' in data:
                                    return data['examples'][:5]
                    except Exception as e:
                        print(f"   Error loading {data_file}: {e}")
                        continue
    
    # If we can't find the cached data, return sample data
    print("⚠️  Using sample data (cached dataset not found)")
    return get_sample_longbench_v2_data()

def get_sample_longbench_v2_data():
    """Get sample LongBench v2 data for demonstration"""
    return [
        {
            "id": "sample_1",
            "category": "single_document_qa",
            "context": "The Renaissance was a period of cultural, artistic, political and economic revival following the Middle Ages. Generally described as taking place from the 14th century to the 17th century, the Renaissance promoted the rediscovery of classical philosophy, literature and art. Some of the greatest thinkers, authors, statesmen, scientists and artists in human history thrived during this era, while global exploration opened up new lands and cultures to European commerce. The Renaissance is credited with bridging the gap between the Middle Ages and modern-day civilization.",
            "question": "According to the passage, the Renaissance is generally described as taking place during which time period?",
            "choices": ["12th to 15th century", "14th to 17th century", "15th to 18th century", "16th to 19th century"],
            "answer": "14th to 17th century"
        },
        {
            "id": "sample_2",
            "category": "multi_document_qa",
            "context": "Document 1: Photosynthesis is the process by which plants use sunlight, water, and carbon dioxide to create oxygen and energy in the form of sugar. Document 2: Cellular respiration is the process by which organisms break down glucose to release energy for cellular activities. Document 3: The carbon cycle describes the movement of carbon through the atmosphere, biosphere, hydrosphere, and geosphere.",
            "question": "Based on the documents, which process directly produces oxygen as a byproduct?",
            "choices": ["Cellular respiration", "Photosynthesis", "Carbon cycle", "Glucose breakdown"],
            "answer": "Photosynthesis"
        },
        {
            "id": "sample_3",
            "category": "long_in_context_learning",
            "context": "Example 1: Input: 'The cat sat on the mat.' Output: 'Simple sentence with subject-verb-object structure.' Example 2: Input: 'Although it was raining, we decided to go for a walk.' Output: 'Complex sentence with dependent and independent clauses.' Example 3: Input: 'She runs every morning, and he swims every evening.' Output: 'Compound sentence with two independent clauses connected by a coordinating conjunction.'",
            "question": "Following the pattern from the examples, analyze this sentence: 'The book that I borrowed from the library is very interesting.'",
            "choices": [
                "Simple sentence with subject-verb-object structure",
                "Complex sentence with dependent and independent clauses", 
                "Compound sentence with coordinating conjunction",
                "Fragment without complete thought"
            ],
            "answer": "Complex sentence with dependent and independent clauses"
        }
    ]

def format_longbench_question(example: Dict) -> str:
    """Format a LongBench v2 question"""
    context = example.get("context", "")
    question = example.get("question", "")
    choices = example.get("choices", [])
    
    formatted = f"Context: {context}\n\nQuestion: {question}\n\n"
    
    if choices:
        formatted += "Choices:\n"
        for i, choice in enumerate(choices):
            formatted += f"{chr(65+i)}. {choice}\n"
        formatted += "\nPlease answer with just the letter (A, B, C, or D):"
    
    return formatted

def simulate_model_response(question: str, correct_answer: str) -> str:
    """Simulate a model response (for demonstration)"""
    
    # In a real scenario, this would be sent to your SGLang server
    # For demo purposes, we'll simulate different response qualities
    
    import random
    
    # Simulate 70% accuracy for demonstration
    if random.random() < 0.7:
        # Return correct answer
        if correct_answer in ["A", "B", "C", "D"]:
            return correct_answer
        else:
            # Find the correct letter
            question_lower = question.lower()
            if "a." in question_lower and correct_answer.lower() in question_lower:
                choices_section = question_lower.split("choices:")[1] if "choices:" in question_lower else ""
                lines = choices_section.split('\n')
                for line in lines:
                    if correct_answer.lower() in line and line.strip().startswith(('a.', 'b.', 'c.', 'd.')):
                        return line.strip()[0].upper()
            return "A"  # Default
    else:
        # Return random incorrect answer
        return random.choice(["A", "B", "C", "D"])

def run_longbench_v2_demo():
    """Run the LongBench v2 demonstration"""
    
    print("🎯 LongBench v2 Evaluation Demonstration")
    print("=" * 50)
    
    # Load sample data
    print("\n📊 Loading LongBench v2 data...")
    examples = load_longbench_v2_sample()
    
    print(f"   Loaded {len(examples)} examples")
    
    # Process each example
    results = {
        "total": len(examples),
        "correct": 0,
        "details": []
    }
    
    print(f"\n🔍 Processing examples...")
    
    for i, example in enumerate(examples):
        print(f"\n--- Example {i+1}/{len(examples)} ---")
        print(f"ID: {example.get('id', 'unknown')}")
        print(f"Category: {example.get('category', 'unknown')}")
        
        # Format the question
        formatted_question = format_longbench_question(example)
        print(f"Question: {example.get('question', '')[:100]}...")
        
        # Get correct answer
        correct_answer = example.get('answer', '')
        
        # Find correct letter
        choices = example.get('choices', [])
        correct_letter = "?"
        if correct_answer in choices:
            correct_letter = chr(65 + choices.index(correct_answer))
        
        # Simulate model response
        predicted_letter = simulate_model_response(formatted_question, correct_answer)
        
        # Check if correct
        is_correct = predicted_letter == correct_letter
        if is_correct:
            results["correct"] += 1
        
        print(f"Expected: {correct_letter} ({correct_answer})")
        print(f"Predicted: {predicted_letter}")
        print(f"Result: {'✅ Correct' if is_correct else '❌ Wrong'}")
        
        results["details"].append({
            "id": example.get('id'),
            "category": example.get('category'),
            "correct_answer": correct_answer,
            "correct_letter": correct_letter,
            "predicted_letter": predicted_letter,
            "is_correct": is_correct
        })
    
    # Calculate final results
    accuracy = results["correct"] / results["total"] if results["total"] > 0 else 0
    
    print(f"\n📈 Final Results:")
    print(f"   Total examples: {results['total']}")
    print(f"   Correct answers: {results['correct']}")
    print(f"   Accuracy: {accuracy:.2%}")
    
    # Save results
    output_file = "/tmp/longbench_v2_demo_results.json"
    with open(output_file, 'w') as f:
        json.dump({
            'demo': True,
            'total_examples': results['total'],
            'correct_answers': results['correct'],
            'accuracy': accuracy,
            'details': results['details']
        }, f, indent=2)
    
    print(f"\n💾 Results saved to: {output_file}")
    
    print(f"\n🎉 Demo completed!")
    print(f"\n📝 To run with a real SGLang server:")
    print(f"   1. Start SGLang server: python -m sglang.launch_server --model <model> --port 8000")
    print(f"   2. Run evaluation: python -m sglang.test.run_eval --eval-name longbench_v2 --model <model> --port 8000 --num-examples 50")
    
    return results

if __name__ == "__main__":
    run_longbench_v2_demo()
