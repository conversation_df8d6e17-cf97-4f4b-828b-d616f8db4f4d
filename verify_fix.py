#!/usr/bin/env python3
"""
Simple verification script to check if the DeepSeek EP B200 fix is properly implemented.
This script checks the code changes without requiring full SGLang dependencies.
"""

import os
import sys
from pathlib import Path

def check_server_args_file():
    """Check if server_args.py has the required changes."""
    server_args_path = Path("python/sglang/srt/server_args.py")
    
    if not server_args_path.exists():
        return False, "server_args.py not found"
    
    content = server_args_path.read_text()
    
    # Check for the new field in ServerArgs class
    if "disable_deepgemm_ue8m0: bool = False" not in content:
        return False, "disable_deepgemm_ue8m0 field not found in ServerArgs"
    
    # Check for the argument parser
    if "--disable-deepgemm-ue8m0" not in content:
        return False, "--disable-deepgemm-ue8m0 argument parser not found"
    
    # Check for help text mentioning B200
    if "Blackwell GPUs (B200)" not in content:
        return False, "Help text mentioning B200 not found"
    
    return True, "server_args.py changes verified"

def check_configurer_file():
    """Check if configurer.py has the required changes."""
    configurer_path = Path("python/sglang/srt/layers/quantization/deep_gemm_wrapper/configurer.py")
    
    if not configurer_path.exists():
        return False, "configurer.py not found"
    
    content = configurer_path.read_text()
    
    # Check for the update function
    if "def update_deepgemm_scale_ue8m0" not in content:
        return False, "update_deepgemm_scale_ue8m0 function not found"
    
    # Check for the global variable modification
    if "global DEEPGEMM_SCALE_UE8M0" not in content:
        return False, "global DEEPGEMM_SCALE_UE8M0 not found"
    
    # Check for the comment about B200 GPUs
    if "DeepSeek EP accuracy issues on B200 GPUs" not in content:
        return False, "Comment about B200 GPU issues not found"
    
    return True, "configurer.py changes verified"

def check_compile_utils_file():
    """Check if compile_utils.py has the required changes."""
    compile_utils_path = Path("python/sglang/srt/layers/quantization/deep_gemm_wrapper/compile_utils.py")
    
    if not compile_utils_path.exists():
        return False, "compile_utils.py not found"
    
    content = compile_utils_path.read_text()
    
    # Check for the import and function call
    if "from sglang.srt.layers.quantization.deep_gemm_wrapper.configurer import update_deepgemm_scale_ue8m0" not in content:
        return False, "import update_deepgemm_scale_ue8m0 not found"
    
    if "update_deepgemm_scale_ue8m0(server_args.disable_deepgemm_ue8m0)" not in content:
        return False, "update_deepgemm_scale_ue8m0 function call not found"
    
    return True, "compile_utils.py changes verified"

def check_deepep_dispatcher():
    """Check if the DeepEP dispatcher uses the DEEPGEMM_SCALE_UE8M0 flag."""
    deepep_path = Path("python/sglang/srt/layers/moe/token_dispatcher/deepep.py")
    
    if not deepep_path.exists():
        return False, "deepep.py not found"
    
    content = deepep_path.read_text()
    
    # Check if the dispatcher uses DEEPGEMM_SCALE_UE8M0
    if "deep_gemm_wrapper.DEEPGEMM_SCALE_UE8M0" not in content:
        return False, "DEEPGEMM_SCALE_UE8M0 usage not found in DeepEP dispatcher"
    
    # Check for the specific usage in dispatch functions
    if "scale_ue8m0=deep_gemm_wrapper.DEEPGEMM_SCALE_UE8M0" not in content:
        return False, "scale_ue8m0 parameter usage not found"
    
    return True, "DeepEP dispatcher verification passed"

def main():
    print("Verifying DeepSeek EP B200 fix implementation...")
    print("=" * 60)
    
    checks = [
        ("Server Args", check_server_args_file),
        ("Configurer", check_configurer_file), 
        ("Compile Utils", check_compile_utils_file),
        ("DeepEP Dispatcher", check_deepep_dispatcher),
    ]
    
    all_passed = True
    
    for name, check_func in checks:
        try:
            success, message = check_func()
            status = "✓" if success else "✗"
            print(f"{status} {name}: {message}")
            if not success:
                all_passed = False
        except Exception as e:
            print(f"✗ {name}: Error during check - {e}")
            all_passed = False
    
    print("=" * 60)
    
    if all_passed:
        print("🎉 All checks passed! The fix is properly implemented.")
        print("\nUsage instructions:")
        print("For B200 GPUs with accuracy issues, add this flag:")
        print("  --disable-deepgemm-ue8m0")
        print("\nExample command:")
        print("  python3 -m sglang.launch_server \\")
        print("    --model deepseek-ai/DeepSeek-V3-0324 \\")
        print("    --tp-size 8 --ep-size 8 \\")
        print("    --trust-remote-code \\")
        print("    --disable-deepgemm-ue8m0")
        return 0
    else:
        print("❌ Some checks failed. The fix may not be complete.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
