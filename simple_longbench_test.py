#!/usr/bin/env python3
"""
Simple LongBench v2 test script that works without full SGLang installation.
This script tests basic functionality with a running SGLang server.
"""

import json
import requests
import time
import argparse
from typing import List, Dict, Any

def test_server_connection(base_url: str) -> bool:
    """Test if the SGLang server is responding"""
    try:
        response = requests.get(f"{base_url}/models", timeout=10)
        if response.status_code == 200:
            models = response.json()
            print(f"✅ Server is responding. Available models: {models}")
            return True
        else:
            print(f"❌ Server responded with status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Failed to connect to server: {e}")
        return False

def send_chat_completion(base_url: str, model: str, messages: List[Dict], temperature: float = 0.0) -> Dict:
    """Send a chat completion request to the server"""
    payload = {
        "model": model,
        "messages": messages,
        "temperature": temperature,
        "max_tokens": 1000
    }
    
    try:
        response = requests.post(f"{base_url}/chat/completions", 
                               json=payload, 
                               timeout=60)
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"❌ Request failed with status {response.status_code}: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return None

def create_test_examples() -> List[Dict]:
    """Create some simple test examples for LongBench v2 style evaluation"""
    return [
        {
            "id": "test_1",
            "category": "single_document_qa",
            "context": "The quick brown fox jumps over the lazy dog. This sentence contains every letter of the English alphabet at least once. It is commonly used as a typing exercise and font display sample.",
            "question": "What animal jumps over the dog in the sentence?",
            "choices": ["cat", "fox", "rabbit", "horse"],
            "answer": "fox"
        },
        {
            "id": "test_2", 
            "category": "single_document_qa",
            "context": "Python is a high-level programming language created by Guido van Rossum in 1991. It emphasizes code readability and simplicity. Python supports multiple programming paradigms including procedural, object-oriented, and functional programming.",
            "question": "Who created the Python programming language?",
            "choices": ["Linus Torvalds", "Guido van Rossum", "Dennis Ritchie", "Bjarne Stroustrup"],
            "answer": "Guido van Rossum"
        },
        {
            "id": "test_3",
            "category": "multi_document_qa", 
            "context": "Document 1: The Earth orbits the Sun once every 365.25 days. Document 2: Mars takes approximately 687 Earth days to complete one orbit around the Sun. Document 3: Venus has an orbital period of about 225 Earth days.",
            "question": "Which planet has the longest orbital period mentioned in the documents?",
            "choices": ["Earth", "Mars", "Venus", "Jupiter"],
            "answer": "Mars"
        }
    ]

def format_question(example: Dict) -> str:
    """Format a question in LongBench v2 style"""
    context = example["context"]
    question = example["question"]
    choices = example["choices"]
    
    formatted = f"Context: {context}\n\nQuestion: {question}\n\nChoices:\n"
    for i, choice in enumerate(choices):
        formatted += f"{chr(65+i)}. {choice}\n"
    
    formatted += "\nPlease answer with just the letter (A, B, C, or D):"
    return formatted

def extract_answer(response_text: str) -> str:
    """Extract answer from model response"""
    response_text = response_text.strip().upper()
    
    # Look for single letter answers
    for letter in ['A', 'B', 'C', 'D']:
        if letter in response_text:
            return letter
    
    return "UNKNOWN"

def run_evaluation(base_url: str, model: str, examples: List[Dict], temperature: float = 0.0) -> Dict:
    """Run evaluation on the examples"""
    results = {
        "total": len(examples),
        "correct": 0,
        "details": []
    }
    
    print(f"\n📊 Running evaluation on {len(examples)} examples...")
    
    for i, example in enumerate(examples):
        print(f"   Processing example {i+1}/{len(examples)}: {example['id']}")
        
        # Format the question
        formatted_question = format_question(example)
        
        messages = [
            {"role": "user", "content": formatted_question}
        ]
        
        # Send request
        response = send_chat_completion(base_url, model, messages, temperature)
        
        if response and "choices" in response:
            model_response = response["choices"][0]["message"]["content"]
            predicted_answer = extract_answer(model_response)
            
            # Check if correct
            correct_letter = chr(65 + example["choices"].index(example["answer"]))
            is_correct = predicted_answer == correct_letter
            
            if is_correct:
                results["correct"] += 1
            
            results["details"].append({
                "id": example["id"],
                "category": example["category"],
                "question": example["question"][:100] + "...",
                "correct_answer": example["answer"],
                "correct_letter": correct_letter,
                "predicted_letter": predicted_answer,
                "model_response": model_response[:200] + "...",
                "is_correct": is_correct
            })
            
            print(f"      Expected: {correct_letter} ({example['answer']})")
            print(f"      Got: {predicted_answer}")
            print(f"      {'✅ Correct' if is_correct else '❌ Wrong'}")
        else:
            print(f"      ❌ Failed to get response")
            results["details"].append({
                "id": example["id"],
                "error": "No response from model"
            })
    
    results["accuracy"] = results["correct"] / results["total"] if results["total"] > 0 else 0
    return results

def main():
    parser = argparse.ArgumentParser(description="Simple LongBench v2 test")
    parser.add_argument("--model", type=str, required=True, help="Model name")
    parser.add_argument("--port", type=int, default=8000, help="Port number")
    parser.add_argument("--host", type=str, default="localhost", help="Host address")
    parser.add_argument("--base-url", type=str, help="Base URL instead of host:port")
    parser.add_argument("--temperature", type=float, default=0.0, help="Temperature")
    
    args = parser.parse_args()
    
    # Prepare base URL
    base_url = args.base_url if args.base_url else f"http://{args.host}:{args.port}/v1"
    
    print(f"🧪 Simple LongBench v2 Test")
    print(f"   Model: {args.model}")
    print(f"   Base URL: {base_url}")
    print(f"   Temperature: {args.temperature}")
    
    # Test server connection
    if not test_server_connection(base_url):
        print(f"\n❌ Cannot connect to server. Make sure SGLang server is running on {base_url}")
        return 1
    
    # Create test examples
    examples = create_test_examples()
    
    # Run evaluation
    results = run_evaluation(base_url, args.model, examples, args.temperature)
    
    # Print results
    print(f"\n📈 Results Summary:")
    print(f"   Total examples: {results['total']}")
    print(f"   Correct answers: {results['correct']}")
    print(f"   Accuracy: {results['accuracy']:.2%}")
    
    # Save results
    output_file = f"/tmp/simple_longbench_test_{args.model.replace('/', '_')}.json"
    with open(output_file, 'w') as f:
        json.dump({
            'model': args.model,
            'base_url': base_url,
            'temperature': args.temperature,
            'results': results
        }, f, indent=2)
    
    print(f"\n💾 Detailed results saved to: {output_file}")
    
    if results['accuracy'] > 0.5:
        print(f"✅ Test completed successfully! Model shows reasonable performance.")
        return 0
    else:
        print(f"⚠️  Test completed but accuracy is low. Check model and server setup.")
        return 0

if __name__ == "__main__":
    import sys
    sys.exit(main())
