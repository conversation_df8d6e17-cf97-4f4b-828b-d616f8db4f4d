# DeepSeek Expert Parallelism B200 Accuracy Fix

## Problem Description

DeepSeek models with expert parallelism (EP) experience accuracy issues on NVIDIA B200 GPUs while working correctly on H200 GPUs. This issue affects the GSM8K benchmark and other accuracy-sensitive workloads.

**Affected Configuration:**
```bash
python3 -m sglang.launch_server --model deepseek-ai/DeepSeek-V3-0324 --tp-size 8 --ep-size 8 --trust-remote-code
python3 benchmark/gsm8k/bench_sglang.py
```

**Symptoms:**
- Works correctly on H200 GPUs (SM90 architecture)
- Produces incorrect results on B200 GPUs (SM100 Blackwell architecture)
- No error messages, but accuracy degradation in model outputs

## Root Cause

The issue is caused by Blackwell-specific optimizations in the DeepEP (Deep Expert Parallelism) dispatcher:

1. **Architecture Detection**: B200 GPUs are automatically detected as Blackwell architecture (SM100)
2. **UE8M0 Scaling**: Blackwell-specific UE8M0 FP8 scaling optimizations are automatically enabled
3. **Accuracy Impact**: These optimizations prioritize performance over numerical precision, causing accuracy degradation

**Key Code Locations:**
- `python/sglang/srt/layers/quantization/deep_gemm_wrapper/configurer.py`: Architecture detection and UE8M0 configuration
- `python/sglang/srt/layers/moe/token_dispatcher/deepep.py`: DeepEP dispatcher using UE8M0 settings

## Solution

Add a new server argument `--disable-deepgemm-ue8m0` to disable Blackwell-specific optimizations when accuracy is critical.

### Implementation

1. **Server Argument**: Added `--disable-deepgemm-ue8m0` flag
2. **Configuration Update**: Modified DeepGEMM configurer to respect the flag
3. **Integration**: Connected server args to DeepGEMM configuration during initialization

### Usage

**For B200 GPUs (with accuracy fix):**
```bash
python3 -m sglang.launch_server \
    --model deepseek-ai/DeepSeek-V3-0324 \
    --tp-size 8 \
    --ep-size 8 \
    --trust-remote-code \
    --disable-deepgemm-ue8m0
```

**For H200 GPUs (no change needed):**
```bash
python3 -m sglang.launch_server \
    --model deepseek-ai/DeepSeek-V3-0324 \
    --tp-size 8 \
    --ep-size 8 \
    --trust-remote-code
```

### Environment Variable Alternative

You can also use the environment variable:
```bash
export SGL_ENABLE_DEEPGEMM_UE8M0=false
python3 -m sglang.launch_server --model deepseek-ai/DeepSeek-V3-0324 --tp-size 8 --ep-size 8 --trust-remote-code
```

## Performance Impact

- **Accuracy**: Restores correct model behavior on B200 GPUs
- **Performance**: Minor performance reduction due to disabled optimizations
- **Compatibility**: No impact on H200 GPUs or other architectures

## Testing

Run the test script to verify the fix:
```bash
python3 test_deepseek_ep_b200_fix.py --integration
```

## Files Modified

1. `python/sglang/srt/server_args.py`: Added `disable_deepgemm_ue8m0` argument
2. `python/sglang/srt/layers/quantization/deep_gemm_wrapper/configurer.py`: Added configuration function
3. `python/sglang/srt/layers/quantization/deep_gemm_wrapper/compile_utils.py`: Integrated with server args
4. `python/sglang/srt/models/longcat_flash.py`: Removed hardcoded `DEEPGEMM_SCALE_UE8M0 = False` that was overriding server settings

## Verification

To verify the fix works:

1. **Before Fix**: GSM8K accuracy degradation on B200
2. **After Fix**: GSM8K accuracy matches H200 performance when using `--disable-deepgemm-ue8m0`

## Bug Resolution (Post-Merge)

**Issue**: Even after the initial fix was merged, users reported that `--disable-deepgemm-ue8m0` still resulted in 0.000 accuracy on B200 GPUs.

**Root Cause**: The DeepSeek model implementation (`longcat_flash.py`) was unconditionally setting `DEEPGEMM_SCALE_UE8M0 = False` during model initialization, overriding the server argument configuration.

**Additional Fix**: Removed the hardcoded `DEEPGEMM_SCALE_UE8M0 = False` line in the model's `__init__` method to allow proper server argument control.

**Code Location**: 
- File: `python/sglang/srt/models/longcat_flash.py:791`
- Issue: `deep_gemm_wrapper.DEEPGEMM_SCALE_UE8M0 = False` was always executed
- Fix: Replaced with explanatory comment about server argument control

## Future Improvements

1. **Automatic Detection**: Could automatically disable UE8M0 for DeepSeek models on Blackwell
2. **Model-Specific Configs**: Add model-specific optimization profiles
3. **Accuracy Benchmarking**: Automated accuracy testing for different GPU architectures

## Related Issues

- GitHub Issue #9943: "[Bug] deepseek ep accuracy issue"
- GitHub Issue #8716: "[Bug] Deepseek V3 crash on 0.4.10 post1" (related FP8 assertion issue)
- GitHub PR #8717: "Remove assertions about per group quant fp8" (related fix)
